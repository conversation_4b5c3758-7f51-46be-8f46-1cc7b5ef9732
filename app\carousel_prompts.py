"""
Carousel prompt generation utilities for LinkedIn carousel creation.
"""
import re
from typing import List, Dict


def get_color_palette_options() -> str:
    """
    Returns available color palette options for AI to choose from dynamically.
    The AI will analyze the content and select the most appropriate palette.
    
    Returns:
        String describing all available color palettes
    """
    return """
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎨 AVAILABLE COLOR PALETTES (AI-SELECTED)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**YOUR TASK**: Analyze the content below and intelligently select THE MOST APPROPRIATE color palette from these options:

1. **Professional Blue** (Best for: Corporate, business, professional services, B2B, consulting)
   - Primary: #0077B5 (LinkedIn Blue)
   - Secondary: #000000 (Black)
   - Accent: #FFFFFF (White)
   - Use when: Content is corporate, professional, business-focused, or general LinkedIn content

2. **Modern Tech** (Best for: Technology, AI, software, digital innovation, startups)
   - Primary: #1E88E5 (Bright Blue)
   - Secondary: #263238 (Dark Slate)
   - Accent: #00BCD4 (Cyan)
   - Use when: Content discusses technology, coding, AI, apps, digital products, innovation

3. **Creative Purple** (Best for: Design, marketing, creative work, branding, media)
   - Primary: #6A4C93 (<PERSON>)
   - Secondary: #1A1A1D (Charcoal)
   - Accent: #C77DFF (Lavender)
   - Use when: Content is about creativity, design, marketing, branding, visual arts

4. **Growth Green** (Best for: Success, growth, achievement, sales, performance)
   - Primary: #2D6A4F (Forest Green)
   - Secondary: #081C15 (Deep Green)
   - Accent: #52B788 (Mint Green)
   - Use when: Content focuses on growth, success, achievements, milestones, results

5. **Energy Orange** (Best for: Bold statements, passion, action, motivation, disruption)
   - Primary: #FF6B35 (Bold Orange)
   - Secondary: #4A0E4E (Deep Purple)
   - Accent: #FFF4E6 (Cream)
   - Use when: Content is energetic, bold, motivational, action-oriented, disruptive

6. **Educational Teal** (Best for: Learning, teaching, training, guides, tips, how-to)
   - Primary: #14B8A6 (Teal)
   - Secondary: #0F172A (Dark Slate)
   - Accent: #FFFFFF (White)
   - Use when: Content is educational, tutorial-based, teaching, sharing knowledge

7. **Minimal Modern** (Best for: Clean, simple, elegant, timeless, sophisticated)
   - Primary: #2C3E50 (Navy Slate)
   - Secondary: #E74C3C (Red Accent)
   - Accent: #ECF0F1 (Light Gray)
   - Use when: Content emphasizes simplicity, minimalism, modern aesthetics, elegance

8. **Premium Gold** (Best for: Luxury, premium, exclusive, high-end, quality)
   - Primary: #C9A227 (Gold)
   - Secondary: #1A1A1A (Rich Black)
   - Accent: #F4E4C1 (Champagne)
   - Use when: Content is about luxury, premium products/services, exclusivity, prestige

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🤖 AI SELECTION INSTRUCTIONS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**ANALYZE THE CONTENT** and consider:
1. Main topic and industry
2. Tone and emotion (professional, creative, energetic, calm)
3. Purpose (educate, inspire, sell, inform)
4. Target audience
5. Overall message and theme

**SELECT ONE PALETTE** that best matches the content's essence.
**DO NOT mix palettes** - use the selected colors consistently across ALL slides.
**IF UNSURE**: Default to Professional Blue (#0077B5) - it works for all content types.

**CRITICAL**: Once you select a palette, USE THOSE EXACT HEX CODES throughout the entire carousel.
"""


def extract_carousel_slides(content: str, max_slides: int = 8) -> List[str]:
    """
    Intelligently extract and structure content into carousel slides.
    Dynamically adapts to content length and structure.
    
    Maximum total slides: 10 (1 intro + up to 8 content + 1 CTA)
    
    Args:
        content: Raw content to be split into slides
        max_slides: Maximum number of content slides (default: 8, excluding intro and CTA)
    
    Returns:
        List of slide content strings (dynamically sized based on content)
    """
    # Split content into lines and filter empty ones
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    
    slides = []
    
    # Check if content has bullet points or numbered lists
    bullet_lines = [l for l in lines if re.match(r'^[\-\*\+•]\s+', l) or re.match(r'^\d+[\.\)]\s+', l)]
    
    if bullet_lines:
        # If we have structured content, each bullet becomes a slide
        # Dynamically limit based on content amount (minimum 3, maximum 8)
        num_bullets = len(bullet_lines)
        slides_to_extract = min(num_bullets, max_slides)
        slides_to_extract = max(3, slides_to_extract)  # At least 3 content slides
        
        for line in bullet_lines[:slides_to_extract]:
            # Remove bullet/number markers
            cleaned = re.sub(r'^[\-\*\+•\d\.\)]+\s+', '', line)
            slides.append(cleaned)
    else:
        # Split by sentences or paragraphs
        # Try to split into meaningful chunks
        text_blob = ' '.join(lines)
        sentences = re.split(r'[.!?]+', text_blob)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 15]
        
        # Group sentences into slides (2-3 sentences per slide or 40-50 words)
        # Dynamically determine target number of slides based on content length
        total_words = len(text_blob.split())
        
        if total_words < 150:
            target_slides = 3  # Short content: minimum 3 slides
        elif total_words < 300:
            target_slides = 5  # Medium content: 5 slides
        else:
            target_slides = min(8, max_slides)  # Long content: up to 8 slides
        
        current_slide = []
        current_word_count = 0
        
        for sentence in sentences:
            words = sentence.split()
            word_count = len(words)
            
            # If adding this sentence keeps us under 50 words, add it
            if current_word_count + word_count <= 50 and len(current_slide) < 3:
                current_slide.append(sentence)
                current_word_count += word_count
            else:
                # Save current slide and start new one
                if current_slide:
                    slides.append('. '.join(current_slide) + '.')
                current_slide = [sentence]
                current_word_count = word_count
            
            # Stop if we have enough slides
            if len(slides) >= target_slides:
                break
        
        # Add remaining content if we haven't reached minimum slides
        if current_slide and len(slides) < target_slides:
            slides.append('. '.join(current_slide) + '.')
        
        # Ensure we have at least 3 content slides
        if len(slides) < 3 and sentences:
            # Add more sentences if available
            remaining_sentences = [s for s in sentences if s not in [item for sublist in [sl.replace('.', '').split('. ') for sl in slides] for item in sublist]]
            for sent in remaining_sentences[:3 - len(slides)]:
                slides.append(sent + '.')
    
    # Ensure we return between 3 and max_slides
    final_slides = slides[:max_slides]
    
    # If we have less than 3 slides, duplicate or expand content
    if len(final_slides) < 3 and len(final_slides) > 0:
        logger_msg = f"Warning: Only {len(final_slides)} slides extracted. Minimum is 3."
        # You can log this or handle it as needed
    
    return final_slides


def get_carousel_prompt(content: str, slide_number: int, total_slides: int, slide_content: str, main_topic: str) -> str:
    """
    Generate a prompt for creating a single carousel slide using GPT Image 1.5.
    Lets AI dynamically select color palette based on content analysis.
    
    Args:
        content: Full original content for context
        slide_number: Current slide number (1-based, where 1 is intro)
        total_slides: Total number of slides in carousel
        slide_content: Content for this specific slide
        main_topic: Main topic/title extracted from content
    
    Returns:
        Detailed prompt for GPT Image 1.5 with AI-driven color selection
    """
    
    # Get color palette options for AI to choose from
    color_palette_options = get_color_palette_options()
    
    # Determine slide type
    is_intro = (slide_number == 1)
    is_conclusion = (slide_number == total_slides)
    is_content = not is_intro and not is_conclusion
    
    # Base specifications for LinkedIn carousels
    base_specs = f"""
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 LINKEDIN CAROUSEL SPECIFICATIONS (CRITICAL)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**MAXIMUM SLIDES**: 10 total (This carousel has {total_slides} slides)
**CURRENT SLIDE**: Slide {slide_number} of {total_slides}
**DIMENSIONS**: 1080 x 1350 pixels (4:5 aspect ratio - mobile-optimized)
**FORMAT**: Vertical portrait orientation
**PLATFORM OPTIMIZATION**: Designed to work seamlessly on iOS, Android, and Web
**STYLE**: Professional, modern, corporate - suitable for LinkedIn business audience
**VISUAL CONSISTENCY**: This is part of a carousel series - maintain consistent branding
**ADAPTIVE DESIGN**: Layout and content adapt to the theme and structure of the content

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📄 CONTENT TO ANALYZE FOR THIS CAROUSEL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

{content[:500]}{"..." if len(content) > 500 else ""}

{color_palette_options}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎨 DESIGN REQUIREMENTS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## COLOR PALETTE SELECTION:
**STEP 1**: Read and analyze the content above
**STEP 2**: Select ONE color palette from the 8 options that best matches the content
**STEP 3**: Use those exact hex codes consistently across this slide and ALL slides in the carousel
**STEP 4**: Note which palette you selected (for consistency across all {total_slides} slides)

**Color Application Rules** (once palette is selected):
- Primary: 60% of visual elements (backgrounds, headers, main icons)
- Secondary: 30% of visual elements (text, supporting elements)
- Accent: 10% of visual elements (highlights, important details)
- Maintain high contrast (minimum 4.5:1 ratio) for readability
- Use the exact hex codes - DO NOT substitute or approximate
- **CRITICAL**: If this is NOT slide 1, maintain the SAME palette you used in previous slides

## TYPOGRAPHY:
- **Headline Font**: Bold, sans-serif (Poppins, Montserrat, or Raleway)
- **Headline Size**: 48-72pt for main title
- **Body Font**: Clean sans-serif (Inter, Open Sans, or Roboto)
- **Body Size**: 24-32pt minimum (must be readable on mobile!)
- **Text Limit**: 25-50 words per slide MAX
- **NO ALL CAPS for body text** (only for small labels)

## LAYOUT PRINCIPLES:
- **Cross-Platform**: Optimized for iOS, Android, and Web viewing
- **Mobile-First**: Design for small screens - everything must be readable on phone
- **White Space**: 60-80px margins from all edges
- **Vertical Flow**: Top-to-bottom reading pattern
- **Clear Hierarchy**: Most important info at top, supporting details below
- **Visual Balance**: Distribute elements evenly across vertical space
- **Responsive Design**: Elements scale appropriately across devices

## VISUAL ELEMENTS:
- Use ONE high-quality icon or illustration per slide (64-96px size)
- Icon style: Modern, minimalist, professional (NO cartoon style)
- Icons in circular or square containers with brand color
- Optional: Subtle background pattern or gradient
- NO stock photos or complex imagery
- **Theme Adaptation**: Visual elements match content theme (tech, business, creative, etc.)

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 CONSISTENCY & ADAPTABILITY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**CRITICAL CONSISTENCY ACROSS ALL {total_slides} SLIDES**:
✓ Same color palette throughout (select ONE from the 8 options and stick to it)
✓ Same font pairing across all slides
✓ Same icon style (modern, minimalist, professional)
✓ Same layout structure (adapted to content type)
✓ Same margin/padding spacing (60-80px from edges)
✓ Consistent slide number indicator: "{slide_number}/{total_slides}" in bottom right corner
✓ Cross-platform compatibility (iOS, Android, Web)

**DYNAMIC ADAPTATION PRINCIPLES**: 
- **Color scheme**: YOU select the most appropriate palette from the 8 options above
  * Analyze the content's tone, industry, purpose, and emotion
  * Choose colors that enhance the message and resonate with the audience
  * Maintain the SAME palette across all {total_slides} slides (crucial for brand consistency)
- **Layout**: Adjusts to content structure (list, process, tips, story, comparison, etc.)
- **Icon style**: Reflects industry/topic (tech icons for tech, business icons for corporate, etc.)
- **Typography weight**: Adapts to message tone (bold for emphasis, light for subtlety)
- **Visual elements**: Match the content theme (modern for tech, classic for business, creative for design)
"""

    if is_intro:
        # First slide: Hook and title
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 SLIDE TYPE: INTRODUCTION / HOOK (Slide {slide_number}/{total_slides})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**PURPOSE**: Grab attention immediately and entice viewer to swipe through

**CONTENT STRUCTURE**:

1. **TOP SECTION** (Upper 1/3):
   - Small eyebrow text: "SWIPE TO LEARN →" or category label (12-16pt)
   - Placement: Top left or top center

2. **MIDDLE SECTION** (Middle 1/2):
   - **HERO HEADLINE**: The main topic/hook (48-72pt, bold, brand color)
   - Make it benefit-driven and intriguing
   - Maximum 2 lines of text
   - Example transformations:
     * "Tips for..." → "X Proven Strategies That Will..."
     * "How to..." → "The Ultimate Guide to..."
     * "Learn about..." → "Master [Topic] in X Simple Steps"

3. **BOTTOM SECTION** (Lower 1/3):
   - Large, eye-catching icon representing the main topic (80-100px)
   - Icon in colored circle/square container
   - Optional: Subtle decorative element or pattern

4. **SLIDE INDICATOR**: Show "{slide_number}/{total_slides}" (small text, bottom right corner)

**MAIN TOPIC FOR THIS CAROUSEL**: 
{main_topic}

**YOUR TASK**: 
Create an attention-grabbing introduction slide that makes people want to swipe.
Use bold, benefit-driven language. Make it visually striking but professional.
'''

    elif is_conclusion:
        # Last slide: CTA and closing
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 SLIDE TYPE: CALL-TO-ACTION / CONCLUSION (Slide {slide_number}/{total_slides})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**PURPOSE**: Drive engagement and provide clear next steps

**CONTENT STRUCTURE**:

1. **TOP SECTION** (Upper 1/4):
   - Headline: "Key Takeaway" or "Ready to Start?" (32-40pt, bold)
   
2. **MIDDLE SECTION** (Middle 1/2):
   - **PRIMARY CTA**: Clear action statement (36-48pt, bold, brand color)
   - Examples:
     * "Which tip will you try first?"
     * "Save this for later!"
     * "Follow for more insights"
     * "Comment your thoughts below"
     * "Share with your network"
   - Keep it conversational and engagement-focused

3. **BOTTOM SECTION** (Lower 1/4):
   - Optional: Secondary CTA or engagement hook (20-24pt)
   - Icon representing action/engagement (64-80px in branded container)
   - Example icons: bookmark, heart, comment bubble, arrow

4. **SLIDE INDICATOR**: Show "{slide_number}/{total_slides}" (bottom right)

**ENGAGEMENT HOOKS TO CONSIDER**:
- "Which point resonated most?"
- "What's your experience with this?"
- "Tag someone who needs to see this"
- "Save this post for reference"
- "Follow @[username] for more"

**YOUR TASK**:
Create a compelling final slide that drives action. Make the CTA clear, specific, and easy to act on.
Use friendly but professional tone. Include visual elements that reinforce the brand.
'''

    else:
        # Content slides: Main points
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 SLIDE TYPE: CONTENT SLIDE (Slide {slide_number}/{total_slides})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**PURPOSE**: Deliver one clear, actionable point

**CONTENT STRUCTURE**:

1. **TOP SECTION** (Upper 1/5):
   - **POINT NUMBER**: Large number badge (e.g., "0{slide_number-1}" or "#{slide_number-1}") (40-56pt, bold)
   - Style: Number in colored circle or square badge with brand color
   - Position: Top left or top center

2. **MIDDLE SECTION** (Middle 3/5):
   - **MAIN POINT**: The headline/key message (32-44pt, bold, dark color)
   - Maximum 2 lines
   - Make it specific and benefit-driven
   
   - **SUPPORTING TEXT** (optional): 1-2 sentences of detail (20-24pt, regular weight)
   - Maximum 25-30 words
   - Use sentence case (NOT all caps)
   - Break into 2-3 short lines for easy reading

3. **BOTTOM SECTION** (Lower 1/5):
   - **ICON**: Visual representation of the point (64-80px)
   - Icon in circular or square container with brand color or gradient
   - Choose meaningful, intuitive icon that matches content
   - Style: Modern, minimalist, professional (NO cartoon)

4. **SLIDE INDICATOR**: Show "{slide_number}/{total_slides}" (small text, bottom right corner)

**CONTENT FOR THIS SLIDE**:
{slide_content}

**YOUR TASK**:
- Extract the CORE MESSAGE from the content above
- Create a clear, concise headline (main point)
- Add brief supporting detail if needed (1-2 short sentences max)
- Choose an appropriate icon that represents the concept
- Design it to be scannable in 3 seconds
- Maintain visual consistency with other slides in series
- Ensure all text is mobile-readable (24pt minimum)
'''

    # Add common footer instructions
    common_footer = f"""

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ FINAL CHECKLIST
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Before finalizing, verify:
☐ Dimensions: 1080 x 1350 pixels (4:5 ratio)
☐ Mobile-readable: All text 24pt minimum
☐ Clear hierarchy: Main message stands out
☐ Ample white space: 60-80px margins
☐ Professional aesthetic: NO cartoon style
☐ Consistent with carousel series
☐ Slide number visible: {slide_number}/{total_slides}
☐ Brand colors used consistently
☐ High contrast for readability (4.5:1 minimum)
☐ All text fully visible (not cut off)
☐ Icon style matches overall design
☐ Can be understood in 3 seconds

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚀 CREATE THIS SLIDE NOW
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Generate a high-quality, professional LinkedIn carousel slide that:
- Is part of a cohesive series
- Looks premium and credible
- Is optimized for mobile viewing
- Drives engagement and shareability
- Maintains brand consistency throughout

Make it visually stunning yet simple. Professional yet engaging. Clear yet compelling! 💼📱
"""

    return prompt + common_footer

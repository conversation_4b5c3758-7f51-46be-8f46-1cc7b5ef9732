version: '3.8'

services:
  linkedin-image-generator:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_CLOUD_PROJECT=ai-linkedin-457504
      - GOOGLE_CLOUD_REGION=us-central1
      - IMAGEN_MODEL_ID=${IMAGEN_MODEL_ID:-imagen-4.0-ultra-generate-preview-06-06}
      # Add your Google Cloud credentials JSON as an environment variable
      # - GOOGLE_APPLICATION_CREDENTIALS_JSON={"type":"service_account",...}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s 
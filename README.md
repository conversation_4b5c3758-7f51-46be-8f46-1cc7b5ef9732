# LinkedIn Image Generator

A FastAPI-based service that generates professional images for LinkedIn posts using Google Cloud Vertex AI's Imagen model with **LinkedIn compliance filtering** and **bias prevention**.

## Features

- **AI-Powered Image Generation**: Uses Google Cloud Vertex AI's Imagen 3.0 model
- **LinkedIn Compliance Filtering**: Automatically detects and blocks content that violates LinkedIn policies
- **Bias Prevention**: Protects against profession discrediting, gender bias, nationality/ethnicity bias, and stereotyping
- **Input Validation**: Comprehensive validation for empty prompts, content length, and data types
- **Professional LinkedIn Optimization**: Automatically analyzes post content and generates appropriate visual prompts
- **Smart Content Analysis**: Extracts key elements and determines visual style based on post content
- **High-Quality Output**: Generates 1024x1024 high-resolution images optimized for LinkedIn
- **RESTful API**: Clean FastAPI endpoints for easy integration
- **Secure Credential Handling**: Supports multiple credential management approaches

## LinkedIn Compliance Features

The service includes comprehensive compliance filtering to ensure all generated images comply with LinkedIn's policies:

### 🔍 Policy Violation Detection

- **Spam Detection**: Identifies promotional content, get-rich-quick schemes, and excessive marketing
- **Harassment Prevention**: Blocks content containing threats, bullying, or harmful language
- **Inappropriate Content**: Filters out adult content, explicit material, and NSFW content
- **Misinformation Detection**: Identifies conspiracy theories and false claims
- **Phishing Prevention**: Detects fake security alerts and account verification scams
- **Hate Speech Filtering**: Blocks discriminatory content and hate speech
- **Violence Prevention**: Filters out violent content and threats
- **Illegal Activities**: Detects references to illegal drugs, fraud, and criminal activities
- **Automation Violations**: Identifies bulk messaging and automation tool usage

### 🛡️ Bias Prevention & Profession Protection

- **Profession Discrediting**: Prevents content that discredits or discriminates against any profession including management and leadership roles
- **Management Protection**: Blocks content like "World's Worst Manager", "bad boss", "terrible leader", "incompetent CEO"
- **Leadership Protection**: Protects executives, directors, supervisors, and all leadership positions
- **Gender Bias Prevention**: Blocks gender-based discrimination and stereotypes
- **Nationality/Ethnicity Bias**: Prevents bias based on nationality, ethnicity, or cultural background
- **Stereotyping Detection**: Identifies harmful generalizations about groups of people
- **Context-Based Bias**: Intelligent detection of bias in professional contexts
- **Inclusive Content Promotion**: Encourages diverse and inclusive professional content

### 🛡️ Compliance Levels

- **High Severity**: Immediate blocking of content (e.g., threats, illegal activities, profession discrediting)
- **Medium Severity**: Warning system for suspicious content
- **Context Analysis**: Intelligent detection of suspicious word combinations and bias patterns
- **Confidence Scoring**: Each violation includes a confidence score for transparency

## Input Validation

The service includes comprehensive input validation to ensure data quality:

### ✅ Validation Rules

- **Content Required**: Content field cannot be empty or missing
- **Content Length**: Must be between 1 and 5000 characters
- **Content Type**: Must be a string (not null, number, or other types)
- **Whitespace Handling**: Trims leading/trailing whitespace and rejects whitespace-only content

### ❌ Validation Error Examples

**Empty Content:**
```json
{
  "content": ""
}
```
**Response:**
```json
{
  "success": false,
  "message": "Bad Request: Content cannot be empty or contain only whitespace",
  "error_type": "validation_error",
  "details": ["Content cannot be empty or contain only whitespace"]
}
```

**Missing Content Field:**
```json
{}
```
**Response:**
```json
{
  "success": false,
  "message": "Bad Request: Missing required field: content",
  "error_type": "validation_error",
  "details": ["Missing required field: content"]
}
```

**Content Too Long:**
```json
{
  "content": "A".repeat(6000)
}
```
**Response:**
```json
{
  "success": false,
  "message": "Bad Request: Field 'content' is too long",
  "error_type": "validation_error",
  "details": ["Field 'content' is too long"]
}
```

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /check-compliance` - Check if content complies with LinkedIn policies
- `POST /generate-image` - Generate image from LinkedIn post content (with compliance check)

### Check Compliance Request

```json
{
  "content": "Your LinkedIn post content here..."
}
```

**Response:**
```json
{
  "is_compliant": true,
  "violations": [],
  "violation_summary": "No violations detected. Content is compliant with LinkedIn policies."
}
```

### Generate Image Request

```json
{
  "content": "Your LinkedIn post content here..."
}
```

**Success Response:** Image file (JPEG)

**Compliance Violation Response:**
```json
{
  "success": false,
  "message": "Image generation blocked due to LinkedIn policy violations. Please review and modify your content.",
  "compliance_check": {
    "is_compliant": false,
    "violations": [
      {
        "violation_type": "profession_discrediting",
        "severity": "high",
        "description": "Detected profession discrediting content: 'world's worst manager'",
        "flagged_content": "world's worst manager",
        "confidence_score": 0.9
      }
    ],
    "violation_summary": "LinkedIn Policy Violations Detected:\n\n• Profession Discrediting:\n  - Detected profession discrediting content: 'world's worst manager' (Severity: high, Confidence: 90.0%)\n\nTotal violations: 1"
  }
}
```

## Testing

### Testing Compliance Features

Run the compliance test suite to see the filtering in action:

```bash
python test_compliance.py
```

This will test various scenarios including:
- ✅ Compliant professional content
- ❌ Spam and promotional content
- ❌ Harassment and harmful content
- ❌ Inappropriate and adult content
- ❌ Misinformation and conspiracy theories
- ❌ Phishing attempts
- ❌ Hate speech and discrimination
- ❌ Violence and threats
- ❌ Illegal activities
- ⚠️ Suspicious combinations and automation indicators

### Testing Bias Prevention Features

Run the bias prevention test suite to test discrimination and bias protection:

```bash
python test_bias_prevention.py
```

This will test various scenarios including:
- ✅ Inclusive and professional content
- ❌ Profession discrediting and discrimination
- ❌ Gender bias and stereotypes
- ❌ Nationality and ethnicity bias
- ❌ Stereotyping and generalizations
- ⚠️ Context-based bias detection
- ✅ Merit-based and inclusive content acceptance

### Testing Validation Features

Run the validation test suite to test input validation:

```bash
python test_validation.py
```

This will test various validation scenarios including:
- ✅ Valid content acceptance
- ❌ Empty content rejection
- ❌ Whitespace-only content rejection
- ❌ Missing field handling
- ❌ Null value handling
- ❌ Content length limits
- ❌ Data type validation

### Manual Testing with curl

```bash
# Test empty content
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": ""}'

# Test profession discrediting
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": "This fake doctor is incompetent and should not be practicing."}'

# Test management profession discrediting
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": "This is the world's worst manager I have ever worked with."}'

# Test leadership profession discrediting
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": "The bad boss is incompetent and doesn't know how to lead."}'

# Test gender bias
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": "Women can not be good engineers because they are too emotional."}'

# Test valid content
curl -X POST http://localhost:8000/check-compliance \
     -H "Content-Type: application/json" \
     -d '{"content": "Our diverse team achieved remarkable success through collaboration."}'
```

## Local Development

### Prerequisites

- Python 3.11+
- Docker (optional)
- Google Cloud credentials

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd linkedin-image-generator
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Google Cloud credentials**
   
   **Option 1: Environment Variable (Recommended)**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS_JSON='{"type":"service_account",...}'
   ```
   
   **Option 2: Service Account File**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account.json
   ```
   
   **Option 3: Default Application Credentials**
   ```bash
   gcloud auth application-default login
   ```

4. **Test compliance features**
   ```bash
   python test_compliance.py
   ```

5. **Test bias prevention features**
   ```bash
   python test_bias_prevention.py
   ```

6. **Test validation features**
   ```bash
   python test_validation.py
   ```

7. **Run the application**
   ```bash
   # Optional: choose a specific Imagen model (defaults to imagen-4-ultra)
   # Windows PowerShell
   $env:IMAGEN_MODEL_ID = "imagen-4-fast"
   # macOS/Linux
   # export IMAGEN_MODEL_ID=imagen-4-fast

   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Using Docker

1. **Build the image**
   ```bash
   docker build -t linkedin-image-generator .
   ```

2. **Run with Docker Compose**
   ```bash
   # Set your credentials as environment variable
   export GOOGLE_APPLICATION_CREDENTIALS_JSON='{"type":"service_account",...}'
   # Optional: pin the Imagen model ID
   export IMAGEN_MODEL_ID=imagen-4-fast
   docker-compose up --build
   ```

3. **Or run directly**
   ```bash
   docker run -p 8000:8000 \
     -e GOOGLE_APPLICATION_CREDENTIALS_JSON='{"type":"service_account",...}' \
     linkedin-image-generator
   ```

## Deployment to Google Cloud Platform

### Option 1: Cloud Run (Recommended)

1. **Enable required APIs**
   ```bash
   gcloud services enable run.googleapis.com
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable aiplatform.googleapis.com
   ```

2. **Set up credentials as a secret (Recommended)**
   ```bash
   # Create a secret with your service account JSON
   echo '{"type":"service_account",...}' | gcloud secrets create google-credentials --data-file=-
   ```

3. **Build and deploy using Cloud Build**
   ```bash
   gcloud builds submit --config cloudbuild.yaml
   ```

4. **Or deploy manually**
   ```bash
   # Build and push to Container Registry
   docker build -t gcr.io/PROJECT_ID/linkedin-image-generator .
   docker push gcr.io/PROJECT_ID/linkedin-image-generator
   
   # Deploy to Cloud Run with secret
   gcloud run deploy linkedin-image-generator \
     --image gcr.io/PROJECT_ID/linkedin-image-generator \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --memory 2Gi \
     --cpu 2 \
     --set-secrets GOOGLE_APPLICATION_CREDENTIALS_JSON=google-credentials:latest
   ```

### Option 2: App Engine

1. **Deploy to App Engine**
   ```bash
   gcloud app deploy app.yaml
   ```

### Option 3: Google Kubernetes Engine (GKE)

1. **Create a cluster**
   ```bash
   gcloud container clusters create linkedin-image-cluster \
     --zone us-central1-a \
     --num-nodes 3
   ```

2. **Create a secret for credentials**
   ```bash
   kubectl create secret generic google-credentials \
     --from-file=credentials.json=/path/to/your/service-account.json
   ```

3. **Deploy the application**
   ```bash
   kubectl apply -f k8s/
   ```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_CLOUD_PROJECT` | Google Cloud Project ID | `ai-linkedin-457504` |
| `GOOGLE_CLOUD_REGION` | Google Cloud Region | `us-central1` |
| `GOOGLE_APPLICATION_CREDENTIALS_JSON` | Service account JSON as string | None |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to service account file | None |
| `IMAGEN_MODEL_ID` | Vertex AI Imagen model identifier to use | `imagen-4-ultra` |

## Security Considerations

- **No credentials in code**: Credentials are handled via environment variables or secrets
- **Non-root container**: The Docker image runs as a non-root user
- **Secure credential storage**: Use Google Cloud Secret Manager for production
- **Environment isolation**: Different credentials for different environments
- **Access control**: Enable authentication for production deployments
- **LinkedIn compliance**: Automatic filtering prevents policy violations
- **Content safety**: Multi-layered content filtering system
- **Input validation**: Prevents malicious or malformed input
- **Bias prevention**: Protects against discrimination and harmful stereotypes

## Monitoring and Logging

- Health check endpoint available at `/health`
- Structured logging with Python's logging module
- Cloud Logging integration for GCP deployments
- Compliance violation logging for audit trails
- Validation error logging for debugging
- Bias detection logging for transparency
- Prometheus metrics can be added for monitoring

## Scaling

- **Cloud Run**: Automatically scales based on traffic
- **App Engine**: Configured for automatic scaling
- **GKE**: Horizontal Pod Autoscaler available

## Cost Optimization

- Use appropriate instance sizes
- Configure auto-scaling policies
- Monitor Vertex AI API usage
- Consider using preemptible instances for development

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify service account permissions
   - Check credential format and content

2. **Validation Errors**
   - Review error messages for specific validation issues
   - Ensure content is not empty and within length limits
   - Check data types (content must be string)

3. **Compliance Violations**
   - Review the violation summary for specific issues
   - Modify content to remove flagged terms
   - Use the `/check-compliance` endpoint to test before generation

4. **Bias Detection**
   - Review flagged content for discriminatory language
   - Ensure content is inclusive and professional
   - Avoid generalizations about groups of people

5. **Image Generation Failures**
   - Check Vertex AI API quotas and limits
   - Verify project has Imagen API enabled
   - Review prompt content for compliance issues

### Compliance Filtering

The compliance system uses a multi-layered approach:

1. **Keyword Detection**: Identifies specific terms and phrases
2. **Pattern Matching**: Uses regex patterns for accurate detection
3. **Context Analysis**: Checks for suspicious word combinations and bias patterns
4. **Confidence Scoring**: Provides transparency on detection accuracy
5. **Severity Classification**: Differentiates between warning and blocking violations

### Bias Prevention

The bias prevention system protects against:

1. **Profession Discrediting**: Prevents discrimination against any profession
2. **Gender Bias**: Blocks gender-based stereotypes and discrimination
3. **Nationality/Ethnicity Bias**: Prevents bias based on cultural background
4. **Stereotyping**: Identifies harmful generalizations
5. **Context Analysis**: Detects bias in professional contexts

### Input Validation

The validation system provides:

1. **Field Validation**: Ensures required fields are present
2. **Type Validation**: Validates data types (string, not null, etc.)
3. **Length Validation**: Enforces content length limits
4. **Content Validation**: Rejects empty or whitespace-only content
5. **Error Reporting**: Provides detailed error messages and HTTP status codes

### Customizing Compliance Rules

To modify compliance rules, edit the `app/compliance.py` file:

```python
# Add new keywords to existing categories
self.spam_keywords['high'].append('new_spam_term')

# Add new violation types
class ViolationType(str, Enum):
    NEW_VIOLATION = "new_violation"

# Add new bias prevention categories
self.new_bias_category = {
    'high': ['bias_term_1', 'bias_term_2'],
    'medium': ['bias_term_3', 'bias_term_4']
}
```

### Customizing Validation Rules

To modify validation rules, edit the `app/schemas.py` file:

```python
class PromptRequest(BaseModel):
    content: str = Field(..., min_length=1, max_length=10000)  # Change length limits
    
    @validator('content')
    def validate_content_not_empty(cls, v):
        # Add custom validation logic
        if 'forbidden_word' in v.lower():
            raise ValueError('Content contains forbidden words')
        return v.strip()
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new compliance rules and bias prevention
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 

apiVersion: apps/v1
kind: Deployment
metadata:
  name: linkedin-image-generator
  labels:
    app: linkedin-image-generator
spec:
  replicas: 3
  selector:
    matchLabels:
      app: linkedin-image-generator
  template:
    metadata:
      labels:
        app: linkedin-image-generator
    spec:
      containers:
      - name: linkedin-image-generator
        image: gcr.io/PROJECT_ID/linkedin-image-generator:latest
        ports:
        - containerPort: 8000
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "ai-linkedin-457504"
        - name: GOOGLE_CLOUD_REGION
          value: "us-central1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/app/google/ai-linkedin-457504-9a444188a552.json"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: google-credentials
          mountPath: /app/google
          readOnly: true
      volumes:
      - name: google-credentials
        secret:
          secretName: google-credentials
---
apiVersion: v1
kind: Service
metadata:
  name: linkedin-image-generator-service
spec:
  selector:
    app: linkedin-image-generator
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: linkedin-image-generator-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: linkedin-image-generator
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80 
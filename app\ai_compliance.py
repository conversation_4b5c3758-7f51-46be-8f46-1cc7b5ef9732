import logging
from typing import Tuple, List, Optional
from vertexai.generative_models import GenerativeModel, GenerationConfig
from app.config import initialize_vertex_ai
from app.schemas import ComplianceViolation, ViolationType
import json
import re

logger = logging.getLogger(__name__)

class AILinkedInComplianceFilter:
    """
    AI-powered LinkedIn compliance filter that uses LLM to understand context
    and make intelligent decisions about policy violations.
    """

    def __init__(self):
        """Initialize the AI compliance filter with Gemini 2.0 Flash."""
        initialize_vertex_ai()
        self.model = GenerativeModel("gemini-2.0-flash")
        
        # LinkedIn Policy Guidelines for the AI
        self.policy_prompt = """You are a LinkedIn content moderation expert. Your job is to evaluate whether an image generation prompt complies with LinkedIn's Professional Community Policies.

LinkedIn's core policies prohibit content that:

1. **Spam and Scams**: Deceptive schemes, get-rich-quick schemes, pyramid schemes, phishing attempts, fraudulent offers
2. **Harassment and Abuse**: Personal attacks, bullying, threats, hate speech targeting individuals
3. **Hate Speech**: Content that attacks, demeans, or discriminates against people based on:
   - Race, ethnicity, national origin
   - Religion or beliefs
   - Gender, gender identity, sexual orientation
   - Disability or medical conditions
   - Age
4. **Violence and Dangerous Content**: Content that promotes, glorifies, or incites violence, terrorism, or dangerous activities
5. **Illegal Activities**: Content promoting illegal drugs, weapons trafficking, human trafficking, fraud, theft
6. **Sexual Content**: Explicit sexual content, nudity, sexual solicitation
7. **Misinformation**: Deliberate false information designed to deceive, especially about health, elections, or public safety
8. **Professional Misconduct**: Content that discredits professions without basis, spreads false credentials, or promotes unethical professional behavior
9. **Discrimination**: Content that promotes discriminatory practices in employment, business, or professional settings

**IMPORTANT CONTEXT CONSIDERATIONS:**

✅ **ALLOWED** - Educational, awareness, or discussion content:
- "Infographic about combating workplace discrimination"
- "Statistics on fighting cybercrime"
- "Presentation on preventing harassment at work"
- "Discussion about addressing bias in hiring"
- "Awareness campaign against pyramid schemes"
- "Educational content about recognizing scams"

❌ **NOT ALLOWED** - Promoting or glorifying harmful content:
- "How to discriminate in hiring"
- "Best pyramid scheme strategies"
- "Ways to harass colleagues"
- "Promoting violence against groups"

**YOUR TASK:**

Analyze the prompt and determine if it violates LinkedIn policies. Consider:
1. **Intent**: Is the content meant to educate/prevent harm OR promote/glorify harm?
2. **Context**: Are sensitive topics discussed professionally or maliciously?
3. **Professional Relevance**: Is this appropriate for a professional networking platform?

Respond ONLY with a valid JSON object in this exact format (no markdown, no code blocks, just raw JSON):

{
  "compliant": true/false,
  "confidence": 0.0-1.0,
  "violations": [
    {
      "type": "violation_type",
      "severity": "high/medium/low",
      "reason": "detailed explanation",
      "flagged_content": "specific phrase or concept"
    }
  ],
  "reasoning": "Overall explanation of your decision and context analysis"
}

If compliant, return empty violations array: "violations": []

Violation types: spam, harassment, hate_speech, violence, illegal_activities, inappropriate_content, misinformation, phishing, profession_discrediting, gender_bias, nationality_ethnicity_bias, stereotyping, automation_violation"""

    def check_compliance(self, prompt: str) -> Tuple[bool, List[ComplianceViolation]]:
        """
        Check if the prompt complies with LinkedIn policies using AI.

        Args:
            prompt: The image generation prompt to check

        Returns:
            Tuple of (is_compliant, violations_list)
        """
        try:
            # Call Gemini to analyze the prompt
            full_prompt = f"{self.policy_prompt}\n\n**PROMPT TO ANALYZE:**\n{prompt}\n\nYour JSON response:"
            
            generation_config = GenerationConfig(
                temperature=0.1,  # Low temperature for consistent, factual responses
                top_p=0.8,
                top_k=40,
                max_output_tokens=2048,
            )
            
            response = self.model.generate_content(
                full_prompt,
                generation_config=generation_config
            )
            
            # Extract and parse the response
            response_text = response.text.strip()
            
            # Remove markdown code blocks if present
            response_text = self._clean_json_response(response_text)
            
            # Parse JSON response
            result = json.loads(response_text)
            
            # Validate response structure
            if not self._validate_response(result):
                logger.error(f"Invalid AI response structure: {result}")
                # Fallback to safe rejection
                return False, [self._create_fallback_violation("Invalid AI response format")]
            
            # Convert to ComplianceViolation objects
            violations = self._parse_violations(result.get('violations', []))
            
            is_compliant = result.get('compliant', False)
            confidence = result.get('confidence', 0.0)
            reasoning = result.get('reasoning', '')
            
            logger.info(f"AI Compliance Check: compliant={is_compliant}, confidence={confidence}, violations={len(violations)}")
            logger.info(f"Reasoning: {reasoning}")
            
            return is_compliant, violations
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            logger.error(f"Response text: {response_text}")
            # Fallback to safe rejection
            return False, [self._create_fallback_violation(f"JSON parsing error: {str(e)}")]
            
        except Exception as e:
            logger.error(f"AI compliance check failed: {str(e)}", exc_info=True)
            # Fallback to safe rejection on any error
            return False, [self._create_fallback_violation(f"AI analysis failed: {str(e)}")]

    def _clean_json_response(self, text: str) -> str:
        """Remove markdown code blocks and clean the JSON response."""
        # Remove markdown code blocks
        text = re.sub(r'^```json\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^```\s*', '', text)
        text = re.sub(r'\s*```$', '', text)
        return text.strip()

    def _validate_response(self, result: dict) -> bool:
        """Validate that the AI response has the required structure."""
        required_keys = ['compliant', 'confidence', 'violations', 'reasoning']
        return all(key in result for key in required_keys)

    def _parse_violations(self, violations_data: List[dict]) -> List[ComplianceViolation]:
        """Convert AI violation data to ComplianceViolation objects."""
        violations = []
        
        for v_data in violations_data:
            try:
                # Map violation type string to ViolationType enum
                violation_type = self._map_violation_type(v_data.get('type', 'inappropriate_content'))
                
                violation = ComplianceViolation(
                    violation_type=violation_type,
                    severity=v_data.get('severity', 'medium'),
                    description=v_data.get('reason', 'Policy violation detected'),
                    flagged_content=v_data.get('flagged_content', ''),
                    confidence_score=float(v_data.get('confidence', 0.8))
                )
                violations.append(violation)
            except Exception as e:
                logger.error(f"Failed to parse violation: {e}")
                continue
        
        return violations

    def _map_violation_type(self, type_string: str) -> ViolationType:
        """Map string violation type to ViolationType enum."""
        type_mapping = {
            'spam': ViolationType.SPAM,
            'harassment': ViolationType.HARASSMENT,
            'hate_speech': ViolationType.HATE_SPEECH,
            'violence': ViolationType.VIOLENCE,
            'illegal_activities': ViolationType.ILLEGAL_ACTIVITIES,
            'inappropriate_content': ViolationType.INAPPROPRIATE_CONTENT,
            'misinformation': ViolationType.MISINFORMATION,
            'phishing': ViolationType.PHISHING,
            'profession_discrediting': ViolationType.PROFESSION_DISCREDITING,
            'gender_bias': ViolationType.GENDER_BIAS,
            'nationality_ethnicity_bias': ViolationType.NATIONALITY_ETHNICITY_BIAS,
            'stereotyping': ViolationType.STEREOTYPING,
            'automation_violation': ViolationType.AUTOMATION_VIOLATION,
        }
        return type_mapping.get(type_string.lower(), ViolationType.INAPPROPRIATE_CONTENT)

    def _create_fallback_violation(self, reason: str) -> ComplianceViolation:
        """Create a fallback violation when AI analysis fails."""
        return ComplianceViolation(
            violation_type=ViolationType.INAPPROPRIATE_CONTENT,
            severity='high',
            description=f"AI moderation system error: {reason}",
            flagged_content="",
            confidence_score=0.5
        )

    def get_violation_summary(self, violations: List[ComplianceViolation]) -> str:
        """Generate a human-readable summary of violations."""
        if not violations:
            return "No violations detected. Content is compliant with LinkedIn policies."
        
        summary_parts = ["LinkedIn Policy Violations Detected (AI Analysis):"]
        
        # Group by violation type
        by_type = {}
        for violation in violations:
            if violation.violation_type.value not in by_type:
                by_type[violation.violation_type.value] = []
            by_type[violation.violation_type.value].append(violation)
        
        for violation_type, type_violations in by_type.items():
            summary_parts.append(f"\n• {violation_type.replace('_', ' ').title()}:")
            for violation in type_violations:
                summary_parts.append(f"  - {violation.description} (Severity: {violation.severity}, Confidence: {violation.confidence_score:.1%})")
        
        summary_parts.append(f"\nTotal violations: {len(violations)}")
        
        return '\n'.join(summary_parts)


# Global instance for AI-based compliance
ai_compliance_filter = AILinkedInComplianceFilter()

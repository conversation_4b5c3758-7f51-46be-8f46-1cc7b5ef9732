from app.config import initialize_vertex_ai
from vertexai.vision_models import ImageGenerationModel
import os
import logging
import base64
import requests
from typing import Optional
import time

_logger = logging.getLogger(__name__)

# Lazy-load and cache the model so the app can start without credentials
_model_instance = None

def _get_model() -> "ImageGenerationModel":
    global _model_instance
    if _model_instance is None:
        initialize_vertex_ai()
        model_id = os.getenv("IMAGEN_MODEL_ID", "imagen-4.0-ultra-generate-preview-06-06")
        _logger.info(f"Using Imagen model: {model_id}")
        _model_instance = ImageGenerationModel.from_pretrained(model_id)
    return _model_instance

def generate_image(user_prompt: str):
    # Use the raw user prompt as-is
    full_prompt = user_prompt.strip()

    # Generate image with retry logic for transient failures
    max_retries = 3
    for attempt in range(max_retries):
        try:
            model = _get_model()
            response = model.generate_images(
                prompt=full_prompt,
                aspect_ratio="1:1",
                number_of_images=1
            )
            
            # Extract and validate image bytes
            image_bytes = _extract_image_bytes_robust(response.images[0])
            
            if image_bytes and len(image_bytes) > 0:
                return image_bytes
            
            # If extraction failed, log and retry
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
                
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            else:
                raise RuntimeError(f"Image generation failed: {str(e)}")
    
    raise RuntimeError("Unable to extract valid image bytes after all attempts")

def _extract_image_bytes_robust(image_obj) -> Optional[bytes]:
    """Robustly extract image bytes from Imagen response object."""
    
    # Method 1: Direct _image_bytes attribute
    if hasattr(image_obj, "_image_bytes") and image_obj._image_bytes:
        return image_obj._image_bytes
    
    # Method 2: Direct image_bytes attribute
    if hasattr(image_obj, "image_bytes") and image_obj.image_bytes:
        return image_obj.image_bytes
    
    # Method 3: to_bytes() method with validation
    if hasattr(image_obj, "to_bytes") and callable(getattr(image_obj, "to_bytes")):
        try:
            bytes_data = image_obj.to_bytes()
            if bytes_data:
                return bytes_data
        except Exception:
            pass
    
    # Method 4: GCS URI fallback
    if hasattr(image_obj, "gcs_uri") and image_obj.gcs_uri:
        try:
            return _download_from_gcs_uri(image_obj.gcs_uri)
        except Exception:
            pass
    
    # Method 5: URI attribute fallback
    if hasattr(image_obj, "uri") and image_obj.uri:
        try:
            return _download_from_uri(image_obj.uri)
        except Exception:
            pass
    
    return None

def _download_from_gcs_uri(gcs_uri: str) -> bytes:
    """Download image from Google Cloud Storage URI."""
    if gcs_uri.startswith("gs://"):
        http_url = gcs_uri.replace("gs://", "https://storage.googleapis.com/")
    else:
        http_url = gcs_uri
    
    response = requests.get(http_url, timeout=30)
    response.raise_for_status()
    return response.content

def _download_from_uri(uri: str) -> bytes:
    """Download image from any HTTP/HTTPS URI."""
    response = requests.get(uri, timeout=30)
    response.raise_for_status()
    return response.content

# imagen-3.0-generate-001 (Imagen 3)
# imagegeneration@005
# model = ImageGenerationModel.from_pretrained("imagen-3.0-generate-001")

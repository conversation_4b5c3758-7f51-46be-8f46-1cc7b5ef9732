import os
import json
from google.oauth2 import service_account
import vertexai
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

PROJECT_ID = os.getenv("GOOGLE_CLOUD_PROJECT", "ai-linkedin-457504")
REGION = os.getenv("GOOGLE_CLOUD_REGION", "us-central1")

# Compliance filter configuration
# Set to "ai" for AI-based compliance (context-aware using Gemini)
# Set to "keyword" for keyword-based compliance (faster but less accurate)
COMPLIANCE_MODE = os.getenv("COMPLIANCE_MODE", "ai")

# Handle credentials from environment variable or file
def get_credentials():
    """Get Google Cloud credentials from environment variable or file"""
    # First, try to get credentials from environment variable
    credentials_json = os.getenv("GOOGLE_APPLICATION_CREDENTIALS_JSON")
    if credentials_json:
        try:
            credentials_info = json.loads(credentials_json)
            return service_account.Credentials.from_service_account_info(credentials_info)
        except (json.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) as e:
            print(f"Error parsing credentials from environment: {e}")
    
    # Fallback to file path
    credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if credentials_path and os.path.exists(credentials_path):
        return service_account.Credentials.from_service_account_file(credentials_path)
    
    # If no credentials found, use default application credentials
    print("No explicit credentials found, using default application credentials")
    return None

CUSTOM_STYLE_PROMPT = """
Generate a high-quality, photorealistic image with the following characteristics:
- High resolution and detailed
- Natural lighting and shadows
- Clean composition with the main subject clearly visible
- No text, watermarks, or distracting elements
- Focus on visual realism and accurate representation of the subject
"""

def initialize_vertex_ai():
    """Initialize Vertex AI with credentials"""
    credentials = get_credentials()
    if credentials:
        vertexai.init(project=PROJECT_ID, location=REGION, credentials=credentials)
    else:
        vertexai.init(project=PROJECT_ID, location=REGION)

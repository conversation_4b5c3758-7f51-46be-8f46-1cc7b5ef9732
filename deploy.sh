#!/bin/bash

# LinkedIn Image Generator Deployment Script
# This script automates the deployment to Google Cloud Platform

set -e

# Configuration
PROJECT_ID=${PROJECT_ID:-"ai-linkedin-457504"}
REGION=${REGION:-"us-central1"}
SERVICE_NAME="linkedin-image-generator"
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is installed
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install it first."
        exit 1
    fi
}

# Set up Google Cloud project
setup_project() {
    print_status "Setting up Google Cloud project..."
    
    # Set the project
    gcloud config set project $PROJECT_ID
    
    # Enable required APIs
    print_status "Enabling required APIs..."
    gcloud services enable run.googleapis.com
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable aiplatform.googleapis.com
    gcloud services enable containerregistry.googleapis.com
}

# Build and push Docker image
build_and_push() {
    print_status "Building Docker image..."
    docker build -t $IMAGE_NAME .
    
    print_status "Pushing image to Container Registry..."
    docker push $IMAGE_NAME
}

# Deploy to Cloud Run
deploy_cloud_run() {
    print_status "Deploying to Cloud Run..."
    
    gcloud run deploy $SERVICE_NAME \
        --image $IMAGE_NAME \
        --platform managed \
        --region $REGION \
        --allow-unauthenticated \
        --memory 2Gi \
        --cpu 2 \
        --max-instances 10 \
        --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLOUD_REGION=$REGION" \
        --port 8000
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    print_status "Service deployed successfully!"
    print_status "Service URL: $SERVICE_URL"
}

# Deploy using Cloud Build
deploy_cloud_build() {
    print_status "Deploying using Cloud Build..."
    
    gcloud builds submit --config cloudbuild.yaml
    
    print_status "Cloud Build deployment completed!"
}

# Main deployment function
main() {
    print_status "Starting deployment process..."
    
    # Check prerequisites
    check_gcloud
    check_docker
    
    # Setup project
    setup_project
    
    # Choose deployment method
    if [ "$1" = "cloudbuild" ]; then
        deploy_cloud_build
    else
        build_and_push
        deploy_cloud_run
    fi
    
    print_status "Deployment completed successfully!"
}

# Show usage
usage() {
    echo "Usage: $0 [cloudbuild]"
    echo ""
    echo "Options:"
    echo "  cloudbuild    Use Cloud Build for deployment (recommended for CI/CD)"
    echo ""
    echo "Environment variables:"
    echo "  PROJECT_ID    Google Cloud Project ID (default: ai-linkedin-457504)"
    echo "  REGION        Google Cloud Region (default: us-central1)"
}

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    usage
    exit 0
fi

# Run main function
main "$@" 
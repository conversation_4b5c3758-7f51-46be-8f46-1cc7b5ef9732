from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional
from enum import Enum

class ViolationType(str, Enum):
    SPAM = "spam"
    HARASSMENT = "harassment"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    MISINFORMATION = "misinformation"
    AUTOMATION_VIOLATION = "automation_violation"
    PHISHING = "phishing"
    HATE_SPEECH = "hate_speech"
    VIOLENCE = "violence"
    ADULT_CONTENT = "adult_content"
    ILLEGAL_ACTIVITIES = "illegal_activities"
    PROFESSION_DISCREDITING = "profession_discrediting"
    GENDER_BIAS = "gender_bias"
    NATIONALITY_ETHNICITY_BIAS = "nationality_ethnicity_bias"
    STEREOTYPING = "stereotyping"

class ComplianceViolation(BaseModel):
    violation_type: ViolationType
    severity: str  # "low", "medium", "high", "critical"
    description: str
    flagged_content: str
    confidence_score: float

class PromptRequest(BaseModel):
    content: str = Field(..., min_length=1, max_length=5000, description="LinkedIn post content to generate image from")
    
    @validator('content')
    def validate_content_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Content cannot be empty or contain only whitespace')
        return v.strip()

class ComplianceResponse(BaseModel):
    is_compliant: bool
    violations: List[ComplianceViolation]
    violation_summary: str

class ImageGenerationResponse(BaseModel):
    success: bool
    message: str
    error_code: Optional[str] = None
    compliance_check: Optional[ComplianceResponse] = None

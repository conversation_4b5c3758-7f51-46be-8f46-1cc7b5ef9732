# Core Web Framework
fastapi
uvicorn
starlette
pydantic
pydantic_core

# Google Cloud Platform & Vertex AI
google-cloud-aiplatform
google-auth
google-api-core
google-cloud-core
google-cloud-storage
google-cloud-bigquery
google-cloud-resource-manager
google-resumable-media
googleapis-common-protos
google-crc32c
google-genai

# OpenAI (for infographic generation)
openai

# Image Processing
Pillow

# HTTP & Networking
requests
httpx
httpcore
urllib3

# Authentication & Security
rsa
pyasn1
pyasn1-modules

# Environment & Configuration
python-dotenv

# Data Processing
numpy
protobuf
proto-plus

# Utilities
typing-extensions
typing-inspection
python-dateutil
six
packaging

# Logging & Monitoring
cachetools

# gRPC (required for Google Cloud services)
grpcio
grpcio-status
grpc-google-iam-v1

# Additional dependencies
annotated-types
anyio
certifi
charset-normalizer
click
colorama
docstring-parser
h11
idna
shapely
sniffio
websockets

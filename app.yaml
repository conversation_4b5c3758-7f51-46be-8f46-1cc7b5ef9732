runtime: python311
service: linkedin-image-generator

env_variables:
  GOOGLE_CLOUD_PROJECT: "ai-linkedin-457504"
  GOOGLE_CLOUD_REGION: "us-central1"
  GOOGLE_APPLICATION_CREDENTIALS: "/app/google/ai-linkedin-457504-9a444188a552.json"

entrypoint: uvicorn app.main:app --host 0.0.0.0 --port $PORT

instance_class: F2

automatic_scaling:
  target_cpu_utilization: 0.6
  min_instances: 1
  max_instances: 10
  target_throughput_utilization: 0.6

resources:
  cpu: 2
  memory_gb: 2
  disk_size_gb: 10

handlers:
  - url: /.*
    script: auto
    secure: always 
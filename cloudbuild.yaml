steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/linkedin-image-generator:$COMMIT_SHA', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/linkedin-image-generator:$COMMIT_SHA']
  
  # Tag the image with 'latest'
  - name: 'gcr.io/cloud-builders/docker'
    args: ['tag', 'gcr.io/$PROJECT_ID/linkedin-image-generator:$COMMIT_SHA', 'gcr.io/$PROJECT_ID/linkedin-image-generator:latest']
  
  # Push the 'latest' tag
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/linkedin-image-generator:latest']
  
  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'linkedin-image-generator'
      - '--image'
      - 'gcr.io/$PROJECT_ID/linkedin-image-generator:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8000'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLOUD_REGION=us-central1'
      # Add your Google Cloud credentials as a secret
      # - '--set-secrets'
      # - 'GOOGLE_APPLICATION_CREDENTIALS_JSON=google-credentials:latest'

images:
  - 'gcr.io/$PROJECT_ID/linkedin-image-generator:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/linkedin-image-generator:latest'

options:
  logging: CLOUD_LOGGING_ONLY 
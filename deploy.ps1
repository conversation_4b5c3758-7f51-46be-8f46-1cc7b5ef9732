# LinkedIn Image Generator Deployment Script for PowerShell
# This script automates the deployment to Google Cloud Platform

param(
    [string]$DeploymentMethod = "manual",
    [string]$ProjectId = "ai-linkedin-457504",
    [string]$Region = "us-central1"
)

# Configuration
$ServiceName = "linkedin-image-generator"
$ImageName = "gcr.io/$ProjectId/$ServiceName"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if gcloud is installed
function Test-GCloud {
    try {
        gcloud version | Out-Null
        return $true
    }
    catch {
        Write-Error "gcloud CLI is not installed. Please install it first."
        return $false
    }
}

# Check if Docker is installed
function Test-Docker {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        Write-Error "Docker is not installed. Please install it first."
        return $false
    }
}

# Set up Google Cloud project
function Set-GoogleCloudProject {
    Write-Status "Setting up Google Cloud project..."
    
    # Set the project
    gcloud config set project $ProjectId
    
    # Enable required APIs
    Write-Status "Enabling required APIs..."
    gcloud services enable run.googleapis.com
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable aiplatform.googleapis.com
    gcloud services enable containerregistry.googleapis.com
}

# Build and push Docker image
function Build-AndPush {
    Write-Status "Building Docker image..."
    docker build -t $ImageName .
    
    Write-Status "Pushing image to Container Registry..."
    docker push $ImageName
}

# Deploy to Cloud Run
function Deploy-CloudRun {
    Write-Status "Deploying to Cloud Run..."
    
    $envVars = "GOOGLE_CLOUD_PROJECT=$ProjectId,GOOGLE_CLOUD_REGION=$Region"
    
    gcloud run deploy $ServiceName `
        --image $ImageName `
        --platform managed `
        --region $Region `
        --allow-unauthenticated `
        --memory 2Gi `
        --cpu 2 `
        --max-instances 10 `
        --set-env-vars $envVars `
        --port 8000
    
    # Get the service URL
    $ServiceUrl = gcloud run services describe $ServiceName --region=$Region --format="value(status.url)"
    Write-Status "Service deployed successfully!"
    Write-Status "Service URL: $ServiceUrl"
}

# Deploy using Cloud Build
function Deploy-CloudBuild {
    Write-Status "Deploying using Cloud Build..."
    
    gcloud builds submit --config cloudbuild.yaml
    
    Write-Status "Cloud Build deployment completed!"
}

# Main deployment function
function Start-Deployment {
    Write-Status "Starting deployment process..."
    
    # Check prerequisites
    if (-not (Test-GCloud)) { exit 1 }
    if (-not (Test-Docker)) { exit 1 }
    
    # Setup project
    Set-GoogleCloudProject
    
    # Choose deployment method
    switch ($DeploymentMethod.ToLower()) {
        "cloudbuild" {
            Deploy-CloudBuild
        }
        default {
            Build-AndPush
            Deploy-CloudRun
        }
    }
    
    Write-Status "Deployment completed successfully!"
}

# Show usage
function Show-Usage {
    Write-Host "Usage: .\deploy.ps1 [-DeploymentMethod <method>] [-ProjectId <id>] [-Region <region>]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -DeploymentMethod    Deployment method: 'manual' or 'cloudbuild' (default: manual)"
    Write-Host "  -ProjectId          Google Cloud Project ID (default: ai-linkedin-457504)"
    Write-Host "  -Region             Google Cloud Region (default: us-central1)"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy.ps1"
    Write-Host "  .\deploy.ps1 -DeploymentMethod cloudbuild"
    Write-Host "  .\deploy.ps1 -ProjectId my-project -Region us-west1"
}

# Check if help is requested
if ($args -contains "-h" -or $args -contains "--help") {
    Show-Usage
    exit 0
}

# Run main deployment function
try {
    Start-Deployment
}
catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
} 
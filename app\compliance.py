import re
import logging
from typing import Dict, List, Tuple, Optional, Set
from app.schemas import ComplianceViolation, ViolationType

logger = logging.getLogger(__name__)

class LinkedInComplianceFilter:
    """
    LinkedIn compliance filter to prevent generation of images that violate LinkedIn policies.
    Implements context-aware analysis to reduce false positives.
    """

    def __init__(self):
        # Define educational/preventive context indicators
        self.educational_context_indicators = {
            'preventing', 'prevent', 'combat', 'combating', 'fight', 'fighting',
            'against', 'anti', 'stop', 'stopping', 'reduce', 'reducing',
            'eliminate', 'eliminating', 'address', 'addressing', 'tackle', 'tackling',
            'challenge', 'challenges', 'issue', 'issues', 'problem', 'problems',
            'concern', 'concerns', 'awareness', 'education', 'educate', 'educating',
            'discuss', 'discussing', 'discussion', 'about', 'regarding', 'concerning',
            'potential for', 'risk of', 'danger of', 'threat of', 'avoid', 'avoiding',
            'ethical', 'ethics', 'responsible', 'responsibility', 'consider', 'considering',
            'question', 'questions', 'debate', 'debating', 'examine', 'examining'
        }

        # Words that require context analysis (not automatically flagged)
        self.context_sensitive_words = {
            'discrimination', 'bias', 'racist', 'bigot', 'segregation',
            'violence', 'attack', 'kill', 'fight', 'war', 'conflict',
            'hate', 'hatred'
        }

        self.spam_keywords = {
            'high': [
                'buy now', 'limited time', 'act fast', 'click here', 'free money',
                'make money fast', 'work from home', 'earn $1000', 'get rich quick',
                'investment opportunity', 'crypto investment', 'bitcoin investment',
                'mlm', 'multi level marketing', 'pyramid scheme', 'get rich',
                'make money online', 'earn money fast', 'quick cash', 'easy money'
            ],
            'medium': [
                'promotion', 'sale', 'discount', 'offer', 'deal', 'bargain',
                'special price', 'limited offer', 'exclusive deal', 'buy today',
                'order now', 'call now', 'text now', 'dm me', 'message me',
                'contact me for details', 'for more info', 'learn more'
            ]
        }
        
        self.harassment_keywords = {
            'high': [
                'kill yourself', 'die', 'you should die', 'hope you die',
                'you deserve to die', 'commit suicide', 'end your life',
                'you\'re worthless', 'you\'re useless', 'you\'re stupid',
                'you\'re an idiot', 'you\'re a moron', 'you\'re dumb'
            ],
            'medium': [
                'hate you', 'dislike you', 'you\'re wrong', 'you\'re bad',
                'you\'re terrible', 'you suck', 'you\'re awful', 'you\'re horrible'
            ]
        }
        
        self.inappropriate_content = {
            'high': [
                'nude', 'naked', 'sex', 'sexual', 'porn', 'pornography',
                'explicit', 'adult content', 'mature content', 'nsfw',
                'not safe for work', 'adult material', 'sexual content'
            ],
            'medium': [
                'sexy', 'attractive', 'beautiful', 'hot', 'gorgeous',
                'stunning', 'appealing', 'alluring', 'seductive'
            ]
        }
        
        self.misinformation_keywords = {
            'high': [
                'fake news', 'conspiracy', 'conspiracy theory', 'government cover up',
                'secret information', 'hidden truth', 'they don\'t want you to know',
                'mainstream media lies', 'fake pandemic', 'fake virus',
                'vaccine conspiracy', '5g conspiracy', 'flat earth',
                'moon landing fake', 'chemtrails', 'illuminati'
            ],
            'medium': [
                'alternative facts', 'real truth', 'actual facts',
                'what they don\'t tell you', 'the real story',
                'mainstream media', 'fake news media'
            ]
        }
        
        self.phishing_keywords = {
            'high': [
                'verify your account', 'account suspended', 'security alert',
                'login attempt', 'unusual activity', 'verify your identity',
                'account locked', 'password expired', 'update your information',
                'confirm your details', 'verify your email', 'verify your phone'
            ],
            'medium': [
                'click here to verify', 'verify now', 'confirm now',
                'update now', 'secure your account', 'protect your account'
            ]
        }
        
        # Updated hate speech keywords - removed context-sensitive words
        # that will be checked separately with context analysis
        self.hate_speech_keywords = {
            'high': [
                'nazi', 'hitler', 'white supremacy', 'racial superiority',
                'ethnic cleansing', 'genocide', 'racial hatred', 'religious hatred',
                'homophobic', 'transphobic', 'antisemitic', 'islamophobic'
                # Removed: 'racist', 'bigot', 'discrimination', 'segregation' - now context-sensitive
            ],
            'medium': [
                'superior race', 'inferior race', 'racial differences',
                'cultural differences', 'traditional values', 'family values'
            ]
        }
        
        # Updated violence keywords - some moved to context-sensitive
        self.violence_keywords = {
            'high': [
                'murder', 'assassinate', 'bomb', 'explode', 'terrorist',
                'terrorism', 'violent', 'assault', 'weapon', 'gun',
                'knife', 'explosive'
                # Removed: 'kill', 'violence', 'attack' - now context-sensitive
            ],
            'medium': [
                'battle', 'struggle', 'defeat',
                'destroy', 'crush', 'eliminate', 'remove'
                # Removed: 'fight', 'war', 'conflict' - now context-sensitive
            ]
        }
        
        self.illegal_activities = {
            'high': [
                'drugs', 'cocaine', 'heroin', 'marijuana', 'weed', 'cannabis',
                'illegal drugs', 'drug dealing', 'drug trafficking',
                'money laundering'
            ],
            'medium': [
                'illegal', 'unlawful', 'criminal', 'crime', 'criminal activity',
                'illegal business', 'underground', 'black market'
            ]
        }
        
        # New category: Profession discrediting
        self.profession_discrediting = {
            'high': [
                'fake doctor', 'fake lawyer', 'fake engineer', 'fake teacher',
                'fake accountant', 'fake nurse', 'fake police', 'fake firefighter',
                'fake pilot', 'fake architect', 'fake scientist', 'fake professor',
                'fake manager', 'fake leader', 'fake executive', 'fake ceo',
                'fake director', 'fake supervisor', 'fake boss', 'fake administrator',
                'unqualified', 'incompetent', 'useless', 'worthless professional',
                'bad doctor', 'bad lawyer', 'bad engineer', 'bad teacher',
                'bad manager', 'bad leader', 'bad boss', 'bad executive',
                'bad ceo', 'bad director', 'bad supervisor', 'bad administrator',
                'incompetent doctor', 'incompetent lawyer', 'incompetent engineer',
                'incompetent manager', 'incompetent leader', 'incompetent boss',
                'incompetent executive', 'incompetent ceo', 'incompetent director',
                'fraudulent professional', 'fake credentials', 'fake degree',
                'unlicensed', 'unregistered', 'illegal practice',
                'world\'s worst manager', 'world\'s worst boss', 'world\'s worst leader',
                'world\'s worst doctor', 'world\'s worst lawyer', 'world\'s worst engineer',
                'world\'s worst teacher', 'world\'s worst nurse', 'world\'s worst police',
                'world\'s worst pilot', 'world\'s worst scientist', 'world\'s worst professor',
                'world\'s worst accountant', 'world\'s worst architect', 'world\'s worst firefighter',
                'worst manager ever', 'worst boss ever', 'worst leader ever',
                'worst doctor ever', 'worst lawyer ever', 'worst engineer ever',
                'worst teacher ever', 'worst nurse ever', 'worst police ever',
                'worst pilot ever', 'worst scientist ever', 'worst professor ever',
                'worst accountant ever', 'worst architect ever', 'worst firefighter ever',
                'terrible manager', 'terrible boss', 'terrible leader',
                'terrible doctor', 'terrible lawyer', 'terrible engineer',
                'terrible teacher', 'terrible nurse', 'terrible police',
                'terrible pilot', 'terrible scientist', 'terrible professor',
                'terrible accountant', 'terrible architect', 'terrible firefighter',
                'awful manager', 'awful boss', 'awful leader',
                'awful doctor', 'awful lawyer', 'awful engineer',
                'awful teacher', 'awful nurse', 'awful police',
                'awful pilot', 'awful scientist', 'awful professor',
                'awful accountant', 'awful architect', 'awful firefighter',
                'horrible manager', 'horrible boss', 'horrible leader',
                'horrible doctor', 'horrible lawyer', 'horrible engineer',
                'horrible teacher', 'horrible nurse', 'horrible police',
                'horrible pilot', 'horrible scientist', 'horrible professor',
                'horrible accountant', 'horrible architect', 'horrible firefighter'
            ],
            'medium': [
                'questionable', 'doubtful', 'suspicious professional',
                'unreliable', 'untrustworthy', 'dodgy', 'shady',
                'amateur', 'inexperienced', 'unskilled', 'untrained',
                'questionable manager', 'questionable boss', 'questionable leader',
                'doubtful manager', 'doubtful boss', 'doubtful leader',
                'suspicious manager', 'suspicious boss', 'suspicious leader',
                'unreliable manager', 'unreliable boss', 'unreliable leader',
                'untrustworthy manager', 'untrustworthy boss', 'untrustworthy leader',
                'dodgy manager', 'dodgy boss', 'dodgy leader',
                'shady manager', 'shady boss', 'shady leader',
                'amateur manager', 'amateur boss', 'amateur leader',
                'inexperienced manager', 'inexperienced boss', 'inexperienced leader',
                'unskilled manager', 'unskilled boss', 'unskilled leader',
                'untrained manager', 'untrained boss', 'untrained leader'
            ]
        }
        
        # New category: Gender bias and discrimination
        self.gender_bias = {
            'high': [
                'women can\'t', 'men can\'t', 'women shouldn\'t', 'men shouldn\'t',
                'women belong in', 'men belong in', 'women are better at',
                'men are better at', 'women are naturally', 'men are naturally',
                'women are emotional', 'men are aggressive', 'women are weak',
                'men are strong', 'women are submissive', 'men are dominant',
                'women should stay home', 'men should work', 'women are inferior',
                'men are superior', 'women are irrational', 'men are logical',
                'women are nurturing', 'men are providers', 'women are caregivers',
                'men are breadwinners', 'women are housewives', 'men are leaders'
            ],
            'medium': [
                'gender roles', 'traditional gender', 'male dominated',
                'female dominated', 'gender specific', 'gender appropriate',
                'masculine job', 'feminine job', 'man\'s work', 'woman\'s work'
            ]
        }
        
        # New category: Nationality and ethnicity bias
        self.nationality_ethnicity_bias = {
            'high': [
                'foreigners can\'t', 'immigrants can\'t', 'outsiders can\'t',
                'people from', 'nationals are', 'ethnic group are',
                'race are', 'culture are', 'foreign workers', 'immigrant workers',
                'outsider workers', 'foreign professionals', 'immigrant professionals',
                'outsider professionals', 'foreign doctors', 'immigrant doctors',
                'foreign lawyers', 'immigrant lawyers', 'foreign engineers',
                'immigrant engineers', 'foreign teachers', 'immigrant teachers',
                'foreign nurses', 'immigrant nurses', 'foreign police',
                'immigrant police', 'foreign pilots', 'immigrant pilots',
                'foreign scientists', 'immigrant scientists', 'foreign professors',
                'immigrant professors', 'foreign accountants', 'immigrant accountants',
                'foreign architects', 'immigrant architects', 'foreign firefighters',
                'immigrant firefighters', 'foreign pilots', 'immigrant pilots'
            ],
            'medium': [
                'local professionals', 'native professionals', 'homegrown',
                'local talent', 'native talent', 'local expertise',
                'native expertise', 'local knowledge', 'native knowledge',
                'local experience', 'native experience', 'local skills',
                'native skills', 'local training', 'native training'
            ]
        }
        
        # New category: Stereotyping and generalizations
        self.stereotyping = {
            'high': [
                'all women are', 'all men are', 'all doctors are',
                'all lawyers are', 'all engineers are', 'all teachers are',
                'all nurses are', 'all police are', 'all pilots are',
                'all scientists are', 'all professors are', 'all accountants are',
                'all architects are', 'all firefighters are', 'all professionals are',
                'all foreigners are', 'all immigrants are', 'all locals are',
                'all natives are', 'all ethnic groups are', 'all races are',
                'all cultures are', 'all religions are', 'all nationalities are'
            ],
            'medium': [
                'most women', 'most men', 'most doctors', 'most lawyers',
                'most engineers', 'most teachers', 'most nurses', 'most police',
                'most pilots', 'most scientists', 'most professors',
                'most accountants', 'most architects', 'most firefighters',
                'most professionals', 'most foreigners', 'most immigrants',
                'most locals', 'most natives', 'most ethnic groups',
                'most races', 'most cultures', 'most religions', 'most nationalities'
            ]
        }
        
        # Compile regex patterns for better performance
        self.patterns = self._compile_patterns()
    
    def _compile_patterns(self) -> Dict[str, Dict[str, List[re.Pattern]]]:
        """Compile regex patterns for all keyword categories."""
        patterns = {}
        
        for category_name, severity_dict in [
            ('spam', self.spam_keywords),
            ('harassment', self.harassment_keywords),
            ('inappropriate', self.inappropriate_content),
            ('misinformation', self.misinformation_keywords),
            ('phishing', self.phishing_keywords),
            ('hate_speech', self.hate_speech_keywords),
            ('violence', self.violence_keywords),
            ('illegal', self.illegal_activities),
            ('profession_discrediting', self.profession_discrediting),
            ('gender_bias', self.gender_bias),
            ('nationality_ethnicity_bias', self.nationality_ethnicity_bias),
            ('stereotyping', self.stereotyping)
        ]:
            patterns[category_name] = {}
            for severity, keywords in severity_dict.items():
                patterns[category_name][severity] = [
                    re.compile(rf'\b{re.escape(keyword)}\b', re.IGNORECASE)
                    for keyword in keywords
                ]
        
        return patterns
    
    def check_compliance(self, prompt: str) -> Tuple[bool, List[ComplianceViolation]]:
        """
        Check if the prompt complies with LinkedIn policies.
        Implements context-aware analysis to reduce false positives.

        Args:
            prompt: The image generation prompt to check

        Returns:
            Tuple of (is_compliant, violations_list)
        """
        violations = []
        prompt_lower = prompt.lower()

        # Check each category (with updated keyword lists)
        violations.extend(self._check_category(prompt_lower, 'spam', ViolationType.SPAM))
        violations.extend(self._check_category(prompt_lower, 'harassment', ViolationType.HARASSMENT))
        violations.extend(self._check_category(prompt_lower, 'inappropriate', ViolationType.INAPPROPRIATE_CONTENT))
        violations.extend(self._check_category(prompt_lower, 'misinformation', ViolationType.MISINFORMATION))
        violations.extend(self._check_category(prompt_lower, 'phishing', ViolationType.PHISHING))
        violations.extend(self._check_category(prompt_lower, 'hate_speech', ViolationType.HATE_SPEECH))
        violations.extend(self._check_category(prompt_lower, 'violence', ViolationType.VIOLENCE))
        violations.extend(self._check_category(prompt_lower, 'illegal', ViolationType.ILLEGAL_ACTIVITIES))
        violations.extend(self._check_category(prompt_lower, 'profession_discrediting', ViolationType.PROFESSION_DISCREDITING))
        violations.extend(self._check_category(prompt_lower, 'gender_bias', ViolationType.GENDER_BIAS))
        violations.extend(self._check_category(prompt_lower, 'nationality_ethnicity_bias', ViolationType.NATIONALITY_ETHNICITY_BIAS))
        violations.extend(self._check_category(prompt_lower, 'stereotyping', ViolationType.STEREOTYPING))

        # Context-sensitive word checking (NEW - reduces false positives)
        violations.extend(self._check_context_sensitive_words(prompt, prompt_lower))

        # Additional checks
        violations.extend(self._check_automation_violations(prompt_lower))
        violations.extend(self._check_context_violations(prompt_lower))
        violations.extend(self._check_profession_bias_context(prompt_lower))

        # Determine if compliant based on violations
        is_compliant = self._evaluate_compliance(violations)

        return is_compliant, violations
    
    def _check_category(self, prompt_lower: str, category: str, violation_type: ViolationType) -> List[ComplianceViolation]:
        """Check a specific category for violations."""
        violations = []
        category_patterns = self.patterns[category]

        for severity, patterns in category_patterns.items():
            for pattern in patterns:
                matches = pattern.findall(prompt_lower)
                if matches:
                    for match in matches:
                        confidence = self._calculate_confidence(severity, len(matches))
                        violation = ComplianceViolation(
                            violation_type=violation_type,
                            severity=severity,
                            description=f"Detected {category.replace('_', ' ')} content: '{match}'",
                            flagged_content=match,
                            confidence_score=confidence
                        )
                        violations.append(violation)

        return violations

    def _check_context_sensitive_words(self, prompt: str, prompt_lower: str) -> List[ComplianceViolation]:
        """
        Check context-sensitive words that may be legitimate in educational/preventive contexts.
        This reduces false positives for words like 'discrimination' when used appropriately.
        """
        violations = []

        for word in self.context_sensitive_words:
            # Check if the word appears in the text
            pattern = re.compile(rf'\b{re.escape(word)}\b', re.IGNORECASE)
            matches = pattern.findall(prompt_lower)

            if matches:
                # Analyze context around the word
                is_educational = self._is_educational_context(prompt_lower, word)

                if not is_educational:
                    # Only flag if NOT in educational context
                    # Determine violation type based on the word
                    violation_type = self._get_violation_type_for_word(word)

                    # Lower confidence since it's context-sensitive
                    confidence = 0.75
                    severity = 'high'

                    violation = ComplianceViolation(
                        violation_type=violation_type,
                        severity=severity,
                        description=f"Detected potentially harmful content: '{word}' (not in educational context)",
                        flagged_content=word,
                        confidence_score=confidence
                    )
                    violations.append(violation)
                else:
                    # Log that we detected but allowed due to context
                    logger.info(f"Word '{word}' detected but allowed due to educational context")

        return violations

    def _is_educational_context(self, prompt_lower: str, word: str) -> bool:
        """
        Determine if a sensitive word appears in an educational or preventive context.
        Returns True if the word is used in a legitimate educational/discussion context.
        """
        # Find the position of the word
        word_pattern = re.compile(rf'\b{re.escape(word)}\b', re.IGNORECASE)
        match = word_pattern.search(prompt_lower)

        if not match:
            return False

        word_pos = match.start()

        # Get context window (150 characters before and after for better analysis)
        context_start = max(0, word_pos - 150)
        context_end = min(len(prompt_lower), word_pos + len(word) + 150)
        context = prompt_lower[context_start:context_end]

        # First, check for NEGATIVE indicators (promoting harmful content)
        negative_indicators = [
            'promote', 'promoting', 'support', 'supporting', 'encourage', 'encouraging',
            'advocate', 'advocating', 'endorse', 'endorsing', 'justify', 'justifying',
            'defend', 'defending', 'need to ' + word, 'should ' + word, 'must ' + word,
            'we should', 'you should', 'they should', 'everyone should'
        ]

        # Check if the word appears in a promoting/advocating context
        for neg_indicator in negative_indicators:
            if neg_indicator in context:
                # Check if it's actually promoting the harmful thing
                # Look for patterns like "promote discrimination" or "support bias"
                promoting_pattern = rf'(promote|promoting|support|supporting|encourage|encouraging|advocate|advocating|endorse|endorsing)\s+\w*\s*{re.escape(word)}'
                if re.search(promoting_pattern, context):
                    return False  # This is NOT educational - it's promoting harm

        # Check for educational indicators in the context
        educational_indicators_found = 0
        for indicator in self.educational_context_indicators:
            if indicator in context:
                educational_indicators_found += 1

        # If we find 2 or more educational indicators, consider it educational context
        if educational_indicators_found >= 2:
            return True

        # Additional heuristics for educational context
        # Check for question marks (indicating discussion/inquiry)
        if '?' in context:
            educational_indicators_found += 1

        # Check for phrases indicating discussion or analysis
        discussion_phrases = [
            'potential for', 'risk of', 'need for', 'importance of',
            'challenge of', 'issue of', 'problem of', 'question of',
            'topic of', 'subject of', 'matter of', 'concern about',
            'thoughts on', 'perspectives on', 'views on', 'opinions on',
            'raises', 'raises questions', 'raises concerns', 'raises issues'
        ]

        for phrase in discussion_phrases:
            if phrase in context:
                educational_indicators_found += 1
                break

        # Require at least 1 educational indicator to pass
        return educational_indicators_found >= 1

    def _get_violation_type_for_word(self, word: str) -> ViolationType:
        """Map context-sensitive words to their appropriate violation types."""
        hate_speech_words = {'discrimination', 'bias', 'racist', 'bigot', 'segregation', 'hatred', 'hate'}
        violence_words = {'violence', 'attack', 'kill', 'fight', 'war', 'conflict'}

        if word in hate_speech_words:
            return ViolationType.HATE_SPEECH
        elif word in violence_words:
            return ViolationType.VIOLENCE
        else:
            return ViolationType.INAPPROPRIATE_CONTENT
    
    def _check_automation_violations(self, prompt_lower: str) -> List[ComplianceViolation]:
        """Check for automation-related violations."""
        violations = []
        
        automation_indicators = [
            'automated', 'bot', 'script', 'scraping', 'data mining',
            'bulk', 'mass', 'batch', 'automation tool', 'auto-generated'
        ]
        
        for indicator in automation_indicators:
            if indicator in prompt_lower:
                violation = ComplianceViolation(
                    violation_type=ViolationType.AUTOMATION_VIOLATION,
                    severity='medium',
                    description=f"Potential automation violation: '{indicator}'",
                    flagged_content=indicator,
                    confidence_score=0.7
                )
                violations.append(violation)
        
        return violations
    
    def _check_context_violations(self, prompt_lower: str) -> List[ComplianceViolation]:
        """Check for context-based violations."""
        violations = []
        
        # Check for suspicious combinations
        suspicious_combinations = [
            ('free', 'money'),
            ('earn', 'fast'),
            ('make', 'quick'),
            ('investment', 'opportunity'),
            ('verify', 'account'),
            ('security', 'alert')
        ]
        
        for combo in suspicious_combinations:
            if combo[0] in prompt_lower and combo[1] in prompt_lower:
                violation = ComplianceViolation(
                    violation_type=ViolationType.SPAM,
                    severity='high',
                    description=f"Suspicious combination detected: '{combo[0]}' and '{combo[1]}'",
                    flagged_content=f"{combo[0]} {combo[1]}",
                    confidence_score=0.8
                )
                violations.append(violation)
        
        return violations
    
    def _check_profession_bias_context(self, prompt_lower: str) -> List[ComplianceViolation]:
        """Check for context-based profession bias and discrimination."""
        violations = []
        
        # Check for profession + bias combinations
        professions = ['doctor', 'lawyer', 'engineer', 'teacher', 'nurse', 'police', 
                      'pilot', 'scientist', 'professor', 'accountant', 'architect', 
                      'firefighter', 'professional', 'worker', 'employee', 'manager',
                      'boss', 'leader', 'executive', 'ceo', 'director', 'supervisor',
                      'administrator', 'supervisor', 'team lead', 'project manager',
                      'department head', 'vice president', 'president', 'chairman',
                      'chairwoman', 'chairperson', 'head', 'chief', 'principal']
        
        bias_indicators = ['bad', 'terrible', 'awful', 'horrible', 'incompetent', 
                          'unqualified', 'fake', 'fraudulent', 'useless', 'worthless',
                          'stupid', 'dumb', 'idiot', 'moron', 'inferior', 'substandard',
                          'worst', 'world\'s worst', 'worst ever', 'terrible', 'awful',
                          'horrible', 'dreadful', 'pathetic', 'hopeless', 'useless']
        
        for profession in professions:
            for bias in bias_indicators:
                if profession in prompt_lower and bias in prompt_lower:
                    # Check if they're close to each other (within 10 words)
                    words = prompt_lower.split()
                    try:
                        prof_idx = words.index(profession)
                        bias_idx = words.index(bias)
                        if abs(prof_idx - bias_idx) <= 10:
                            violation = ComplianceViolation(
                                violation_type=ViolationType.PROFESSION_DISCREDITING,
                                severity='high',
                                description=f"Profession discrediting detected: '{profession}' and '{bias}'",
                                flagged_content=f"{profession} {bias}",
                                confidence_score=0.9
                            )
                            violations.append(violation)
                    except ValueError:
                        continue
        
        return violations
    
    def _calculate_confidence(self, severity: str, match_count: int) -> float:
        """Calculate confidence score based on severity and match count."""
        base_scores = {
            'high': 0.9,
            'medium': 0.7,
            'low': 0.5
        }
        
        base_score = base_scores.get(severity, 0.5)
        match_multiplier = min(match_count * 0.1, 0.2)  # Cap at 0.2 additional
        
        return min(base_score + match_multiplier, 1.0)
    
    def _evaluate_compliance(self, violations: List[ComplianceViolation]) -> bool:
        """
        Evaluate overall compliance based on violations.
        Updated to handle context-sensitive violations more appropriately.
        """
        if not violations:
            return True

        # Check for critical violations (high severity with good confidence)
        critical_violations = [v for v in violations if v.severity == 'high' and v.confidence_score >= 0.75]
        if critical_violations:
            return False

        # Check for multiple medium violations
        medium_violations = [v for v in violations if v.severity == 'medium' and v.confidence_score > 0.7]
        if len(medium_violations) >= 3:
            return False

        # Check for high confidence violations (very confident about any violation)
        high_confidence_violations = [v for v in violations if v.confidence_score >= 0.9]
        if high_confidence_violations:
            return False

        return True
    
    def get_violation_summary(self, violations: List[ComplianceViolation]) -> str:
        """Generate a human-readable summary of violations."""
        if not violations:
            return "No violations detected. Content is compliant with LinkedIn policies."
        
        summary_parts = ["LinkedIn Policy Violations Detected:"]
        
        # Group by violation type
        by_type = {}
        for violation in violations:
            if violation.violation_type.value not in by_type:
                by_type[violation.violation_type.value] = []
            by_type[violation.violation_type.value].append(violation)
        
        for violation_type, type_violations in by_type.items():
            summary_parts.append(f"\n• {violation_type.replace('_', ' ').title()}:")
            for violation in type_violations:
                summary_parts.append(f"  - {violation.description} (Severity: {violation.severity}, Confidence: {violation.confidence_score:.1%})")
        
        summary_parts.append(f"\nTotal violations: {len(violations)}")
        
        return '\n'.join(summary_parts)

# Global instance
compliance_filter = LinkedInComplianceFilter() 
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
import uvicorn
import os
import tempfile
from openai import OpenAI
from dotenv import load_dotenv
import logging
import uuid
import base64
from PIL import Image
from io import BytesIO
from app.prompts import get_infographic_prompt, preprocess_content

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Load Environment Variables and Initialize Clients ---
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")

# Initialize OpenAI client
openai_client = None
if not api_key or api_key == "YOUR_API_KEY_HERE":
    logging.error("OpenAI API key is missing or not set.")
else:
    try:
        openai_client = OpenAI(api_key=api_key)
        logging.info("OpenAI client initialized successfully.")
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}", exc_info=True)
        openai_client = None

def generate_infographic_image(prompt: str, size: str) -> str:
    """Generate an infographic image using OpenAI's GPT Image 1.5 model (gpt-image-1.5)"""
    if openai_client is None:
        logging.error("OpenAI client is not initialized.")
        return None

    logging.info(f"Generating infographic image with GPT Image 1.5, size {size}...")
    try:
        response = openai_client.images.generate(
            model="gpt-image-1.5",
            prompt=prompt,
            size=size,
            quality="medium",
            n=1
        )
        logging.info("Received response from GPT Image 1.5.")
        # Get the base64 image
        image_base64 = response.data[0].b64_json
        image_bytes = base64.b64decode(image_base64)
        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
        image_path = temp_file.name
        temp_file.close()
        # Write the image bytes
        with open(image_path, 'wb') as f:
            f.write(image_bytes)
        logging.info(f"Image saved successfully to: {image_path}")
        return image_path
    except Exception as e:
        logging.error(f"Error generating image with GPT Image 1.5: {e}", exc_info=True)
        return None

# --- FastAPI App Setup ---

app = FastAPI(
    title="LinkedIn Infographics Generator",
    description="Generate professional infographics from LinkedIn post content using GPT Image 1.5",
    version="2.4.0"
)

# Pydantic model for the request body
class InfographicRequest(BaseModel):
    content: str

# Root endpoint
@app.get("/")
async def read_root():
    return {
        "message": "LinkedIn Infographics Generator API v2.4.0 (GPT Image 1.5)",
        "description": "Generate professional infographics from your LinkedIn content using GPT Image 1.5",
        "endpoints": {
            "POST /generate_infographic": "Generate an infographic from text content",
            "GET /health": "Health check endpoint"
        }
    }

# Endpoint to generate the infographic
@app.post("/generate_infographic")
async def generate_infographic_endpoint(request: InfographicRequest):
    """
    Generates an infographic image (PNG) from LinkedIn post content using GPT Image 1.5.
    
    Parameters:
    - content: The LinkedIn post content to convert into an infographic
    
    Returns the generated infographic image file.
    """
    raw_content = request.content.strip()
    
    if not raw_content:
        logging.warning("Received empty content.")
        raise HTTPException(status_code=400, detail="Input text cannot be empty.")

    if openai_client is None:
        logging.error("OpenAI client not initialized.")
        raise HTTPException(status_code=500, detail="Server configuration error: OpenAI service not available. Check API key.")

    # Preprocess content: remove hashtags and condense if too long
    logging.info(f"Preprocessing content (original length: {len(raw_content)} chars)...")
    content = preprocess_content(raw_content)
    logging.info(f"Content preprocessed (new length: {len(content)} chars)")

    # Analyze content to select the best image dimension dynamically
    def analyze_content_complexity(text):
        """
        Analyze content complexity to determine optimal image dimensions.
        Returns a tuple of (points_count, content_length, word_count, has_structure).
        """
        lines = [l.strip() for l in text.split('\n') if l.strip()]

        # Count different types of structured content
        bullet_points = [l for l in lines if l.startswith(('- ', '* ', '+ ', '•'))]
        numbered_points = [l for l in lines if len(l) >= 2 and l[:2].replace('.', '').replace(')', '').isdigit()]

        # Get the maximum count of structured points
        points_count = max(len(bullet_points), len(numbered_points), len(lines))

        # Calculate content metrics
        content_length = len(text)
        word_count = len(text.split())

        # Check if content has clear structure (bullets or numbers)
        has_structure = len(bullet_points) > 0 or len(numbered_points) > 0

        return points_count, content_length, word_count, has_structure

    # Analyze the content
    points, content_length, word_count, has_structure = analyze_content_complexity(content)

    logging.info(f"Content analysis: {points} points, {content_length} chars, {word_count} words, structured: {has_structure}")

    # Smart dimension selection based on content complexity
    # Optimized to use smaller (cheaper) sizes when appropriate
    
    # Very short and simple content - use square (cheapest: $0.034)
    if points <= 2 and content_length <= 150 and word_count <= 25:
        size = "1024x1024"
        aspect_ratio = "1:1"
        reason = "very short content - using square format (cost-effective)"
    
    # Long content or many points - use landscape ($0.05)
    elif points >= 4 or content_length > 250 or word_count > 40:
        size = "1536x1024"
        aspect_ratio = "16:9"
        reason = f"content-heavy ({points} points, {content_length} chars, {word_count} words) - using landscape"
    
    # Medium content - use portrait ($0.05)
    else:
        size = "1024x1536"
        aspect_ratio = "9:16"
        reason = f"medium content ({points} points, {content_length} chars, {word_count} words) - using portrait"

    logging.info(f"Selected size: {size}, aspect ratio: {aspect_ratio}")
    logging.info(f"Selection reason: {reason}")

    # Generate prompt with dynamic dimensions
    prompt = get_infographic_prompt(content, style="professional", aspect_ratio=aspect_ratio, size=size)

    # Generate the infographic image
    image_path = generate_infographic_image(prompt, size)

    if not image_path:
        logging.error("Image generation failed.")
        raise HTTPException(status_code=500, detail="Failed to generate infographic image. Please try again.")

    # Return the image file using FileResponse
    file_name = f"linkedin_infographic_{uuid.uuid4().hex[:8]}.png"
    logging.info(f"Returning generated infographic: {image_path}")

    return FileResponse(
        path=image_path,
        media_type="image/png",
        filename=file_name,
    )
